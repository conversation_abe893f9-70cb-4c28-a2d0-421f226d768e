import { z } from "zod";
import { categories } from "../config/categories";
import { zValidateString } from "./ZodSchemaMaster";

export const CreateStoreApiSchema = z
  .object({
    name: zValidateString({
      fieldName: "Store Name",
      minLength: 2,
      maxLength: 63,
      regex: /^[a-zA-Z0-9][a-zA-Z0-9-()\/ .,'"]*$/,
    }),

    category: zValidateString({
      fieldName: "Store Category"
    })
    .refine((value) => {
      return categories.some((category) => category.name === value);
    },
    {
      message: "Selected Category Does Not Exist"
    })
  });

export const UpdateStoreApiSchema = z
  .object({
    name: zValidateString({
      fieldName: "Store Name",
      minLength: 2,
      maxLength: 63,
      regex: /^[a-zA-Z0-9][a-zA-Z0-9-()\/ .,'"]*$/,
    }).optional(),

    appearance: z.object({
      title: zValidateString({
        fieldName: "Store Title",
        minLength: 1,
        maxLength: 15,
      }).optional(),
      favicon: z.string().url("Invalid favicon URL").or(z.literal("")).optional(),
      logo: z.string().url("Invalid logo URL").or(z.literal("")).optional(),
      color_scheme: z.string().regex(/^#[0-9A-Fa-f]{6}$/, "Invalid color scheme format").optional(),
      theme: z.enum(["default", "modern", "classic"]).optional(),
      font_family: z.string().min(1, "Font family cannot be empty").optional(),
    }).optional(),

    domain: z.object({
      custom_domain: z.string().regex(/^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.[a-zA-Z]{2,}$/, "Invalid domain format").or(z.literal("")).optional(),
      under_maintainance: z.boolean().optional(),
    }).optional(),

    contact: z.object({
      phone: z.string().regex(/^[+]?[0-9\s-()]*$/, "Invalid phone format").or(z.literal("")).optional(),
      email: z.string().email("Invalid email format").or(z.literal("")).optional(),
      address: z.string().max(255, "Address too long").or(z.literal("")).optional(),
    }).optional(),

    settings: z.object({
      category: zValidateString({
        fieldName: "Store Category"
      })
      .refine((value) => {
        return categories.some((category) => category.name === value);
      },
      {
        message: "Selected Category Does Not Exist"
      }).optional(),
      currency: z.string().length(3, "Currency must be 3 characters").optional(),
      language: z.string().regex(/^[a-z]{2}-[A-Z]{2}$/, "Invalid language format").optional(),
      timezone: z.string().min(1, "Timezone cannot be empty").optional(),
      date_format: z.enum(["AD", "BS"]).optional(),
    }).optional(),

    social: z.array(z.object({
      platform: z.string().min(1, "Platform name required"),
      url: z.string().url("Invalid social media URL"),
    })).optional(),
  })
  .refine((data) => Object.keys(data).length > 0, {
    message: "At least one field must be provided for update"
  });