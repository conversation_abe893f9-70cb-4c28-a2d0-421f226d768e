const getUserPermissionsInStore = () => ({
  $lookup: {
    from: "Roles",
    let: {
      storeId: { $arrayElemAt: ["$currentUserInStore._id", 0] },
      roleId: {
        $arrayElemAt: [
          {
            $reduce: {
              input: "$currentUserInStore.users",
              initialValue: [],
              in: {
                $concatArrays: [
                  "$$value",
                  {
                    $map: {
                      input: {
                        $filter: {
                          input: "$$this",
                          as: "user",
                          cond: { $eq: ["$$user.user_id", "$_id"] }
                        }
                      },
                      as: "userFiltered",
                      in: "$$userFiltered.role_id"
                    }
                  }
                ]
              }
            },
          },
          0
        ]
      }
    },
    pipeline: [
      {
        $match: {
          $expr: {
            $and: [
              { $eq: ["$store", "$$storeId"] },
              { $eq: ["$_id", "$$roleId"] }
            ]
          }
        }
      },
      {
        $project: {
          _id: 1,
          permissions: 1,
        }
      }
    ],
    as: "userPermissionsInStore"
  }
});

export default getUserPermissionsInStore;