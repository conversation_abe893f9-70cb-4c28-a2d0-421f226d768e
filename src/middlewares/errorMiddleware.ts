import { parseError } from "../functions/errors";
import type { CustomError } from "../typings/CustomError";
import type { Request, Response, NextFunction } from "express";

interface ActivityLog {
  url: string;
  ip: string;
  storeId: string | null;
  userId: string | null;
  requestBody: any;
  queryParams: any;
  responseStatus: number;
  method: string;
  timestamp: Date;
}

export default function errorMiddleware(error: CustomError, req: Request, res: Response, _next: NextFunction) {
  const statusCode = error.statusCode || 500;

  // Log error activity
  logErrorActivity(req, statusCode);

  res.status(statusCode).json(parseError(error));
}

function logErrorActivity(req: Request, responseStatus: number) {
  try {
    // Skip logging if database is not available
    if (!req.db) {
      return;
    }

    // Get client IP address
    const ip = req.ip ||
               req.socket.remoteAddress ||
               req.headers['x-forwarded-for'] as string ||
               req.headers['x-real-ip'] as string ||
               'unknown';

    const activityLog: ActivityLog = {
      url: req.path,
      ip: Array.isArray(ip) ? ip[0] : ip,
      storeId: req.storeId || null,
      userId: req.userId || null,
      requestBody: req.body || {},
      queryParams: req.query || {},
      responseStatus,
      method: req.method,
      timestamp: new Date()
    };

    // Insert activity log into database (fire and forget)
    const activityLogs = req.db.collection("ActivityLogs");
    activityLogs.insertOne(activityLog).catch((error: any) => {
      console.error("Failed to log error activity:", error);
    });
  } catch (error: any) {
    console.error("Error in error activity logging:", error);
  }
}
