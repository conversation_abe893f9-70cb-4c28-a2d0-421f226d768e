import type { Request, Response, NextFunction } from "express";

export const loggerMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const start = process.hrtime();
  const chunks: any[] = [];

  const originalWrite = res.write;
  const originalEnd = res.end;

  // Capture response body
  res.write = function (chunk: any, ...args: any[]) {
    try {
      chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk));
    } catch (_) {}

    // @ts-ignore
    return originalWrite.apply(res, [chunk, ...args]);
  };

  res.end = function (chunk: any, ...args: any[]) {
    try {
      if (chunk) {
        chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk));
      }
    } catch (_) {}

    // @ts-ignore
    return originalEnd.apply(res, [chunk, ...args]);
  };

  const logResponse = () => {
    const [seconds, nanoseconds] = process.hrtime(start);
    const durationMs = parseFloat((seconds * 1000 + nanoseconds / 1e6).toFixed(2));

    let responseBody: any = null;
    try {
      const bodyString = Buffer.concat(chunks).toString("utf8");
      responseBody = JSON.parse(bodyString);
    } catch {
      try {
        responseBody = Buffer.concat(chunks).toString("utf8");
      } catch {
        responseBody = null;
      }
    }

    const log = {
      method: req.method,
      url: decodeURI(req.originalUrl),
      basePath: req.baseUrl || req.path,
      query: req.query,
      body: req.body,
      userId: (req as any).userId || null,
      statusCode: res.statusCode,
      statusMessage: res.statusMessage,
      response: responseBody,
      durationMs,
      ip: req.ip || req.socket.remoteAddress,
    };

    console.log(JSON.stringify(log, null, 2));
  };

  res.on("finish", logResponse); // successful response
  res.on("close", logResponse);  // client aborted or error

  next();
};
