import { z, type ZodIssue } from "zod";

export class FatalError extends <PERSON><PERSON>r {
  statusCode: number;
  field: string;

  constructor(statusCode: number, message: string, field = "") {
    super(message);
    this.statusCode = statusCode;
    this.field = field;
  }
}

function zodErrorFields(errors: ZodIssue[]) {
  const uniquePaths = new Set<string>();

  errors.forEach((error) => {
    uniquePaths.add(error.path.join("."));
  });

  return Array.from(uniquePaths);
}

function zodErrorMessages(errors: ZodIssue[]) {
  const messages = [] as string[];
  const uniquePaths = new Set<string>();
  errors.forEach((error) => {
    if (!uniquePaths.has(error.path.join("."))) {
      uniquePaths.add(error.path.join("."));
      messages.push(error.message);
    }
  });
  return messages;
}

export function parseError(error: any) {
  console.log("Error caught in parseError:\n", error);

  if (error instanceof z.ZodError) {
    return {
      statusCode: 400,
      message: zodErrorMessages(error.errors),
      fields: zodErrorFields(error.errors)
    };
  }

  return {
    statusCode: error.statusCode || 500,
    message: [error.message],
    fields: [error.field ?? ""]
  };
}
