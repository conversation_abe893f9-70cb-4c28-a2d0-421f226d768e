import express from "express";
import { createStoreAction, getStoreAction, getStoresAction, updateStoreAction } from "./storeController";

const storeRoutes = express.Router();

storeRoutes.post("/create-store", createStoreAction);
storeRoutes.post("/get-stores", getStoresAction);
storeRoutes.post("/get-store", getStoreAction);
storeRoutes.post("/update-store", updateStoreAction);

export default storeRoutes;