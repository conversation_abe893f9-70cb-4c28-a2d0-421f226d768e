// @ts-nocheck
/**
 * Generates a MongoDB update object with dot notation from nested object.
 *
 * @param data - The nested object to convert (must be a plain object).
 * @param prefix - The prefix to use for the keys (internal use for recursion).
 * @returns The converted update object with dot notation keys.
 *
 * @example
 * Suppose we have the following data in database:
 *
 * const data = {
 *   name: "Bibek Oli",
 *   address: {
 *      city: "Jhapa",
 *      state: "Koshi Pradesh",
 *      country: {
 *        name: "Nepal",
 *        code: "NP"
 *      }
 *    }
 *  }
 *
 * @example
 * If we want to update only name and country name by passing the following data:
 *
 * const newData = {
 *  name: "<PERSON>",
 *  address: {
 *    country: {
 *      name: "USA"
 *    }
 *  }
 * }
 *
 * @example
 * We cannot pass the above data directly to the database. Doing so will override entire address object.
 * So, we need to convert the above data to the following format so that we can update only name and country name:
 *
 * const exampleData = {
 *   name: "Mark Zuck<PERSON>berg",
 *   "address.country.name": "USA"
 * }
 *
 * @example
 * To convert the above data to the required format, we can use the following code.
 * It will convert the nested object to the required format and we can use this data to update only the required fields:
 *
 * const updateObject = generateUpdateObject(newData);
 * console.log(updateObject);
 *
 * Output:
 * {
 *   name: "Mark Zuckerberg",
 *   "address.country.name": "USA"
 * }
 */

/**
 * Checks if a value is a plain object (not null, array, Date, etc.)
 */
const isPlainObject = (value: unknown): value is Record<string, unknown> => {
  return (
    value !== null &&
    typeof value === "object" &&
    !Array.isArray(value) &&
    !(value instanceof Date) &&
    !(value instanceof RegExp)
  );
};

/**
 * Generates a MongoDB update object with dot notation from nested object
 */
const generateUpdateObject = (
  data: Record<string, unknown>,
  prefix: string = ""
): Record<string, unknown> => {
  // Input validation
  if (!data || typeof data !== "object" || Array.isArray(data) || data instanceof Date || data instanceof RegExp) {
    throw new Error("Input must be a plain object");
  }

  const updateObject: Record<string, unknown> = {};

  for (const key in data) {
    // Skip inherited properties and undefined values
    if (!data.hasOwnProperty(key) || data[key] === undefined) {
      continue;
    }

    const prefixedKey = prefix ? `${prefix}.${key}` : key;
    const value = data[key];

    if (isPlainObject(value)) {
      // Recursively process nested objects
      const nestedUpdate = generateUpdateObject(value, prefixedKey);

      // Merge nested updates directly into updateObject (more efficient than Object.assign)
      for (const nestedKey in nestedUpdate) {
        updateObject[nestedKey] = nestedUpdate[nestedKey];
      }
    } else {
      // Handle primitive values, arrays, null, Date objects, etc.
      updateObject[prefixedKey] = value;
    }
  }

  return updateObject;
};

export default generateUpdateObject;

// Type definitions
export type UpdateObject = Record<string, unknown>;
export type NestedObject = Record<string, unknown>;