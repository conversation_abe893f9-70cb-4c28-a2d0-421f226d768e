import { z } from "zod";
import { zValidateEmail, zValidatePassword, zValidateString } from "./ZodSchemaMaster";

export const DeleteSessionApiSchema = z
  .object({
    sessionId: zValidateString({
      fieldName: "Session Id",
      length: 32
    })
  });

export const UpdatePasswordApiSchema = z
  .object({
    currentPassword: zValidatePassword({
      fieldName: "Current Password",
    }),

    newPassword: zValidatePassword({
      fieldName: "New Password",
    }),

    confirmPassword: zValidatePassword({
      fieldName: "Confirm Password",
    }),
  })
  .superRefine((data, ctx) => {
    if (data.newPassword !== data.confirmPassword) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "New Passwords Do Not Match",
        path: ["confirmPassword"],
      });
    }

    if (data.currentPassword === data.newPassword) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "New Password Cannot Be Same As Current Password",
        path: ["newPassword"],
      });
    }
  });

export const LoginApiSchema = z
  .object({
    email: zValidateEmail({
      fieldName: "Email",
    })
    .optional(),

    phone: zValidateString({
      fieldName: "Phone Number",
      length: 10,
      regex: /^(97|98)\d{8}$/
    })
    .optional(),

    password: zValidatePassword({
      fieldName: "Password",
    })
    .optional(),
  })
  .superRefine((data, ctx) => {
    if (!data.email && !data.phone) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Either Email Or Phone Is Required",
        path: ["email"],
      });
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Either Email Or Phone Is Required",
        path: ["phone"],
      });
    }
  
    if (!data.password) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Password Is Required",
        path: ["password"],
      });
    }
  })
  .transform((data) => ({
    email: data.email,
    phone: data.phone,
    password: data.password as string,
  }));

export const SignUpApiSchema = z
  .object({
    name: zValidateString({
      fieldName: "Name",
      maxLength: 50,
    })
    .optional(),

    email: zValidateEmail({
      fieldName: "Email",
    })
    .optional(),

    phone: zValidateString({
      fieldName: "Phone Number",
      length: 10,
      regex: /^(97|98)\d{8}$/
    })
    .optional(),

    password: zValidatePassword({
      fieldName: "Password",
    })
    .optional(),
  })
  .superRefine((data, ctx) => {
    if (!data.name) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Name Is Required",
        path: ["name"],
      });
    }

    if (!data.email && !data.phone) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Either Email Or Phone Is Required",
        path: ["email"],
      });
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Either Email Or Phone Is Required",
        path: ["phone"],
      });
    }

    if (!data.password) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Password Is Required",
        path: ["password"],
      });
    }
  })
  .transform((data) => ({
    name: data.name as string,
    email: data.email,
    phone: data.phone,
    password: data.password as string,
  }));