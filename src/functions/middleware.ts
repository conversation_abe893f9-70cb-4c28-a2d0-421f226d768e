import { Actions, Controllers } from "../enums/baseEnum";
import type { Action, Controller } from "../enums/baseEnum";
import type { Request } from "express";

export const getControllerAndAction = (req: Request) => {
  // supposed to be in the format: /api/v1/controller/action/...
  const parts = req.path.split("/").filter(Boolean);

  const controller = parts[2] as Controller;
  const action = parts[3] as Action;

  return {
    controller,
    action
  };
};

export const getAuthTokenAndStoreId = (req: Request) => {
  const { controller, action } = getControllerAndAction(req);
  const whiteListedControllersForStoreId = [Controllers.Auth, Controllers.User] as Controller[];
  const whiteListedActionsForStoreId = [Actions.Store.GetStores, Actions.Store.CreateStore] as Action[];

  const cookies = req.cookies || {};

  const authToken = (req.header("xe-auth-token") || cookies["xe-auth-token"] || "").trim() || null;
  const storeId = (req.header("xe-store-id") || cookies["xe-store-id"] || "").trim() || null;

  const isStoreIdOptional = whiteListedControllersForStoreId.some((ctrler) => ctrler === controller) || whiteListedActionsForStoreId.some((actn) => actn === action);

  return {
    authToken,
    storeId,
    isStoreIdOptional
  };
};