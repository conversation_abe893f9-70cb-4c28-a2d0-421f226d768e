import http from "http";
import cors from "cors";
import express from "express";
import cookieParser from "cookie-parser";
import dotenv from "dotenv";
dotenv.config();

import dbMiddleware from "./middlewares/dbMiddleware";
import authMiddleware from "./middlewares/authMiddleware";
import loggerMiddleware from "./middlewares/loggerMiddleware";
import notFoundMiddleware from "./middlewares/notFoundMiddleware";
import errorMiddleware from "./middlewares/errorMiddleware";

import authRoutes from "./routes/auth/authRoutes";
import userRoutes from "./routes/user/userRoutes";
import storeRoutes from "./routes/store/storeRoutes";

const app = express();
const server = http.createServer(app);
app.use(express.json());
app.use(cors());
app.use(cookieParser());

app.use(dbMiddleware);
app.use(authMiddleware);
app.use(loggerMiddleware);

app.use("/api/v1/auth", authRoutes);
app.use("/api/v1/user", userRoutes);
app.use("/api/v1/store", storeRoutes);

app.use(notFoundMiddleware);
app.use(errorMiddleware);

server.listen(process.env.PORT, () => {
  console.log(`Server Running on Port ${process.env.PORT}`);
});

export default app;