import generateUpdateObject from "./generateUpdateObject";

describe("generateUpdateObject", () => {
  // Basic functionality tests
  describe("Basic functionality", () => {
    test("should handle flat object", () => {
      const input = { name: "<PERSON>", age: 30, active: true };
      const expected = { name: "<PERSON>", age: 30, active: true };
      expect(generateUpdateObject(input)).toEqual(expected);
    });

    test("should handle nested object", () => {
      const input = {
        name: "<PERSON>",
        address: {
          city: "NYC",
          country: "USA"
        }
      };
      const expected = {
        name: "<PERSON>",
        "address.city": "NYC",
        "address.country": "USA"
      };
      expect(generateUpdateObject(input)).toEqual(expected);
    });

    test("should handle deeply nested object", () => {
      const input = {
        user: {
          profile: {
            personal: {
              name: "<PERSON>",
              age: 30
            },
            contact: {
              email: "<EMAIL>"
            }
          }
        }
      };
      const expected = {
        "user.profile.personal.name": "<PERSON>",
        "user.profile.personal.age": 30,
        "user.profile.contact.email": "<EMAIL>"
      };
      expect(generateUpdateObject(input)).toEqual(expected);
    });
  });

  // Primitive value tests
  describe("Primitive values", () => {
    test("should handle string values", () => {
      const input = { name: "<PERSON>", nested: { title: "Mr." } };
      const expected = { name: "John", "nested.title": "Mr." };
      expect(generateUpdateObject(input)).toEqual(expected);
    });

    test("should handle number values", () => {
      const input = { age: 30, score: 95.5, nested: { count: 0 } };
      const expected = { age: 30, score: 95.5, "nested.count": 0 };
      expect(generateUpdateObject(input)).toEqual(expected);
    });

    test("should handle boolean values", () => {
      const input = { active: true, verified: false, nested: { enabled: true } };
      const expected = { active: true, verified: false, "nested.enabled": true };
      expect(generateUpdateObject(input)).toEqual(expected);
    });

    test("should handle null values", () => {
      const input = { deletedAt: null, nested: { avatar: null } };
      const expected = { deletedAt: null, "nested.avatar": null };
      expect(generateUpdateObject(input)).toEqual(expected);
    });
  });

  // Array handling tests
  describe("Array handling", () => {
    test("should treat arrays as primitive values", () => {
      const input = {
        tags: ["red", "blue", "green"],
        nested: {
          items: [1, 2, 3],
          categories: ["A", "B"]
        }
      };
      const expected = {
        tags: ["red", "blue", "green"],
        "nested.items": [1, 2, 3],
        "nested.categories": ["A", "B"]
      };
      expect(generateUpdateObject(input)).toEqual(expected);
    });

    test("should handle empty arrays", () => {
      const input = { emptyArray: [], nested: { items: [] } };
      const expected = { emptyArray: [], "nested.items": [] };
      expect(generateUpdateObject(input)).toEqual(expected);
    });

    test("should handle arrays with objects", () => {
      const input = {
        users: [{ name: "John" }, { name: "Jane" }],
        nested: {
          data: [{ id: 1 }, { id: 2 }]
        }
      };
      const expected = {
        users: [{ name: "John" }, { name: "Jane" }],
        "nested.data": [{ id: 1 }, { id: 2 }]
      };
      expect(generateUpdateObject(input)).toEqual(expected);
    });
  });

  // Special object types
  describe("Special object types", () => {
    test("should handle Date objects as primitives", () => {
      const date1 = new Date("2023-01-01");
      const date2 = new Date("2023-12-31");
      const input = {
        createdAt: date1,
        nested: {
          updatedAt: date2
        }
      };
      const expected = {
        createdAt: date1,
        "nested.updatedAt": date2
      };
      expect(generateUpdateObject(input)).toEqual(expected);
    });

    test("should handle RegExp objects as primitives", () => {
      const regex1 = /test/g;
      const regex2 = /pattern/i;
      const input = {
        pattern: regex1,
        nested: {
          validation: regex2
        }
      };
      const expected = {
        pattern: regex1,
        "nested.validation": regex2
      };
      expect(generateUpdateObject(input)).toEqual(expected);
    });
  });

  // Edge cases and undefined handling
  describe("Edge cases", () => {
    test("should skip undefined values", () => {
      const input = {
        name: "John",
        age: undefined,
        nested: {
          title: "Mr.",
          description: undefined
        }
      };
      const expected = {
        name: "John",
        "nested.title": "Mr."
      };
      expect(generateUpdateObject(input)).toEqual(expected);
    });

    test("should handle empty objects", () => {
      const input = {};
      const expected = {};
      expect(generateUpdateObject(input)).toEqual(expected);
    });

    test("should handle nested empty objects", () => {
      const input = {
        name: "John",
        emptyNested: {},
        address: {
          city: "NYC",
          emptySubNested: {}
        }
      };
      const expected = {
        name: "John",
        "address.city": "NYC"
      };
      expect(generateUpdateObject(input)).toEqual(expected);
    });

    test("should handle objects with only undefined values", () => {
      const input = {
        name: undefined,
        nested: {
          title: undefined,
          age: undefined
        }
      };
      const expected = {};
      expect(generateUpdateObject(input)).toEqual(expected);
    });
  });

  // Mixed data types
  describe("Mixed data types", () => {
    test("should handle complex mixed object", () => {
      const input = {
        name: "John Doe",
        age: 30,
        active: true,
        tags: ["developer", "javascript"],
        createdAt: new Date("2023-01-01"),
        deletedAt: null,
        profile: {
          bio: "Software Developer",
          skills: ["React", "Node.js"],
          experience: {
            years: 5,
            companies: ["TechCorp", "StartupXYZ"],
            current: {
              company: "BigTech",
              position: "Senior Developer",
              startDate: new Date("2022-01-01"),
              remote: true
            }
          },
          social: {
            twitter: "@johndoe",
            github: null,
            linkedin: undefined // should be skipped
          }
        }
      };

      const expected = {
        name: "John Doe",
        age: 30,
        active: true,
        tags: ["developer", "javascript"],
        createdAt: new Date("2023-01-01"),
        deletedAt: null,
        "profile.bio": "Software Developer",
        "profile.skills": ["React", "Node.js"],
        "profile.experience.years": 5,
        "profile.experience.companies": ["TechCorp", "StartupXYZ"],
        "profile.experience.current.company": "BigTech",
        "profile.experience.current.position": "Senior Developer",
        "profile.experience.current.startDate": new Date("2022-01-01"),
        "profile.experience.current.remote": true,
        "profile.social.twitter": "@johndoe",
        "profile.social.github": null
        // linkedin should be omitted due to undefined
      };

      expect(generateUpdateObject(input)).toEqual(expected);
    });
  });

  // Error cases
  describe("Error handling", () => {
    test("should throw error for null input", () => {
      expect(() => generateUpdateObject(null as any)).toThrow("Input must be a plain object");
    });

    test("should throw error for undefined input", () => {
      expect(() => generateUpdateObject(undefined as any)).toThrow("Input must be a plain object");
    });

    test("should throw error for array input", () => {
      expect(() => generateUpdateObject([1, 2, 3] as any)).toThrow("Input must be a plain object");
    });

    test("should throw error for string input", () => {
      expect(() => generateUpdateObject("string" as any)).toThrow("Input must be a plain object");
    });

    test("should throw error for number input", () => {
      expect(() => generateUpdateObject(123 as any)).toThrow("Input must be a plain object");
    });

    test("should throw error for Date input", () => {
      expect(() => generateUpdateObject(new Date() as any)).toThrow("Input must be a plain object");
    });

    test("should throw error for RegExp input", () => {
      expect(() => generateUpdateObject(/test/ as any)).toThrow("Input must be a plain object");
    });
  });

  // Real-world MongoDB update scenarios
  describe("Real-world scenarios", () => {
    test("should handle user profile update", () => {
      const input = {
        name: "Updated Name",
        profile: {
          avatar: "new-avatar.jpg",
          settings: {
            notifications: true,
            theme: "dark"
          }
        }
      };
      const expected = {
        name: "Updated Name",
        "profile.avatar": "new-avatar.jpg",
        "profile.settings.notifications": true,
        "profile.settings.theme": "dark"
      };
      expect(generateUpdateObject(input)).toEqual(expected);
    });

    test("should handle store update scenario", () => {
      const input = {
        name: "Updated Store Name",
        description: "New description",
        settings: {
          isActive: true,
          features: {
            inventory: true,
            analytics: false
          }
        },
        updatedAt: new Date()
      };
      const result = generateUpdateObject(input);

      expect(result.name).toBe("Updated Store Name");
      expect(result.description).toBe("New description");
      expect(result["settings.isActive"]).toBe(true);
      expect(result["settings.features.inventory"]).toBe(true);
      expect(result["settings.features.analytics"]).toBe(false);
      expect(result.updatedAt).toBeInstanceOf(Date);
    });
  });
});
