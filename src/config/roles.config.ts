import { Actions, Controllers } from "../enums/baseEnum";

const permissionsEnum  = {
  READ: "READ",
  CREATE: "CREATE",
  UPDATE: "UPDATE",
  DELETE: "DELETE",
  ACTIVATE: "ACTIVATE",
  DEACTIVATE: "DEACTIVATE"
} as const;

export const allPermissions = [
  {
    id: "UPDATE_STORE",
    label: "Update Store",
    controller: Controllers.Store,
    action: Actions.Store.UpdateStore,
    permission: permissionsEnum.UPDATE,
  }
] as const;

export const roleIds = allPermissions.map((permission) => permission.id);

export const getPermissionByActionName = (action: string) => allPermissions.find((permission) => permission.action === action); 