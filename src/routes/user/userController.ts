import jwt from "jsonwebtoken";
import { ObjectId } from "mongodb";
import { FatalError } from "../../functions/errors";
import { hashPassword } from "../../functions/cryptography";
import { UpdatePasswordApiSchema } from "../../schema/UserApiSchema";
import type { NextFunction, Request, Response } from "express";

export const getProfileAction = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const users = req.db.collection("Users");

    const user = await users.findOne(
      {
        _id: new ObjectId(req.userId),
        "sessions.session_id": req.sessionId
      },
      {
        projection: {
          _id: 1,
          name: 1,
          email: 1,
          email_verified: 1,
          phone: 1,
          phone_verified: 1,
          sessions: {
            $map: {
              input: "$sessions",
              as: "session",
              in: {
                $mergeObjects: [
                  "$$session",
                  {
                    session_id: {
                      $concat: [
                        {
                          $substr: ["$$session.session_id", 0, 11]
                        },
                        "**********",
                        {
                          $substr: ["$$session.session_id", 21, 11]
                        }
                      ]
                    }
                  }
                ]
              }
            }
          }
        }
      }
    );

    if (!user) {
      throw new FatalError(403, "Invalid User Session");
    }

    await users.findOneAndUpdate(
      {
        _id: new ObjectId(req.userId),
        "sessions.session_id": req.sessionId
      },
      {
        $set: {
          "sessions.$.last_online": new Date()
        }
      }
    );

    const newJwtToken = {
      _id: req.userId,
      session_id: req.sessionId
    };

    const jwtToken = jwt.sign(newJwtToken, process.env.JWT_SECRET as string, {
      expiresIn: "14d",
      algorithm: "HS512",
    });

    res.status(200).json({
      user,
      token: jwtToken
    });
  }
  catch (error: any) {
    next(error);
  }
}

export const updatePasswordAction = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const validatedData = UpdatePasswordApiSchema.parse(req.body);
    const users = req.db.collection("Users");

    const user = await users.findOneAndUpdate(
      {
        _id: new ObjectId(req.userId),
        password: hashPassword(validatedData.currentPassword),
      },
      {
        $set: {
          password: hashPassword(validatedData.newPassword)
        }
      }
    );

    if (!user) {
      throw new FatalError(403, "Invalid Current Password", "currentPassword");
    }

    res.status(200).json({
      message: "Password Updated Successfully"
    });
  }
  catch (error: any) {
    next(error);
  }
}