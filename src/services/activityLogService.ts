import { ObjectId } from "mongodb";
import ConnectToDatabase from "../mongodb/connect";
import { ActivityTypes, ResourceTypes, type ActivityType, type ResourceType } from "../schema/ActivityLogSchema";
import type { Request, Response } from "express";

interface LogActivityParams {
  userId: string;
  storeId?: string;
  action: ActivityType;
  resource: ResourceType;
  resourceId?: string;
  method: string;
  endpoint: string;
  ipAddress?: string;
  userAgent?: string;
  statusCode: number;
  requestData?: Record<string, any>;
  responseData?: Record<string, any>;
  errorMessage?: string;
  durationMs?: number;
}

export class ActivityLogService {
  /**
   * Log an activity to the database
   */
  static async logActivity(params: LogActivityParams): Promise<void> {
    try {
      const { db } = await ConnectToDatabase();
      const activityLogs = db.collection("ActivityLogs");

      const logEntry = {
        user_id: new ObjectId(params.userId),
        store_id: params.storeId ? new ObjectId(params.storeId) : null,
        action: params.action,
        resource: params.resource,
        resource_id: params.resourceId ? new ObjectId(params.resourceId) : null,
        method: params.method,
        endpoint: params.endpoint,
        ip_address: params.ipAddress || null,
        user_agent: params.userAgent || null,
        status_code: params.statusCode,
        request_data: params.requestData || null,
        response_data: params.responseData || null,
        error_message: params.errorMessage || null,
        duration_ms: params.durationMs || null,
        timestamp: new Date(),
      };

      await activityLogs.insertOne(logEntry);
    } catch (error) {
      // Don't throw errors for logging failures to avoid breaking the main flow
      console.error("Failed to log activity:", error);
    }
  }

  /**
   * Helper method to extract activity info from request
   */
  static extractActivityInfo(req: Request, res: Response): {
    action: ActivityType;
    resource: ResourceType;
    resourceId?: string;
  } {

    // @ts-ignore
    const { controller, action } = req.path.split("/").filter(Boolean);
    
    // Map controller/action to activity types
    const activityMapping: Record<string, { action: ActivityType; resource: ResourceType }> = {
      // Auth activities
      "auth/login": { action: ActivityTypes.LOGIN, resource: ResourceTypes.SESSION },
      "auth/signup": { action: ActivityTypes.SIGNUP, resource: ResourceTypes.USER },
      "auth/delete-session": { action: ActivityTypes.DELETE_SESSION, resource: ResourceTypes.SESSION },
      
      // Store activities
      "store/create-store": { action: ActivityTypes.CREATE_STORE, resource: ResourceTypes.STORE },
      "store/update-store": { action: ActivityTypes.UPDATE_STORE, resource: ResourceTypes.STORE },
      "store/get-store": { action: ActivityTypes.VIEW_STORE, resource: ResourceTypes.STORE },
      "store/get-stores": { action: ActivityTypes.VIEW, resource: ResourceTypes.STORE },
      
      // User activities
      "user/get-profile": { action: ActivityTypes.VIEW, resource: ResourceTypes.USER },
      "user/update-password": { action: ActivityTypes.UPDATE_PASSWORD, resource: ResourceTypes.USER },
    };

    const key = `${controller}/${action}`;
    const mapping = activityMapping[key];

    if (mapping) {
      return {
        action: mapping.action,
        resource: mapping.resource,
        resourceId: req.storeId || req.userId,
      };
    }

    // Default mapping based on HTTP method
    const methodMapping: Record<string, ActivityType> = {
      GET: ActivityTypes.VIEW,
      POST: ActivityTypes.CREATE,
      PUT: ActivityTypes.UPDATE,
      PATCH: ActivityTypes.UPDATE,
      DELETE: ActivityTypes.DELETE,
    };

    return {
      action: methodMapping[req.method] || ActivityTypes.VIEW,
      resource: ResourceTypes.STORE, // Default resource
      resourceId: req.storeId,
    };
  }

  /**
   * Get activity logs for a user or store
   */
  static async getActivityLogs(filters: {
    userId?: string;
    storeId?: string;
    action?: ActivityType;
    resource?: ResourceType;
    limit?: number;
    skip?: number;
  }) {
    try {
      const { db } = await ConnectToDatabase();
      const activityLogs = db.collection("ActivityLogs");

      const query: any = {};
      
      if (filters.userId) query.user_id = new ObjectId(filters.userId);
      if (filters.storeId) query.store_id = new ObjectId(filters.storeId);
      if (filters.action) query.action = filters.action;
      if (filters.resource) query.resource = filters.resource;

      const logs = await activityLogs
        .find(query)
        .sort({ timestamp: -1 })
        .limit(filters.limit || 50)
        .skip(filters.skip || 0)
        .toArray();

      return logs;
    } catch (error) {
      console.error("Failed to get activity logs:", error);
      return [];
    }
  }
}
