import { ObjectId } from "mongodb";
import ConnectToDatabase from "../../mongodb/connect";
import { FatalError } from "../../functions/errors";
import { generateRandomString } from "../../functions/cryptography";
import { CreateStoreApiSchema, UpdateStoreApiSchema } from "../../schema/StoreApiSchema";
import { globalValuesEnum } from "../../enums/globalValuesEnum";
import { statusTypeEnum } from "../../enums/statusTypeEnum";
import { planTypeEnum } from "../../enums/planTypeEnum";
import { roleIds } from "../../config/roles.config";
import { createSlug, getRandomColor } from "../../functions/common";
import type { NextFunction, Request, Response } from "express";
import generateUpdateObject from "../../mongodb/generateUpdateObject";

export const createStoreAction = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const validatedData = CreateStoreApiSchema.parse(req.body);
    const { db, client } = await ConnectToDatabase();

    const session = client.startSession();
    const stores = db.collection("Stores");
    const roles = db.collection("Roles");
    const customers = db.collection("Customers");

    await session.withTransaction(async () => {
      const ownerRoleResponse = await roles.insertOne({
        name: globalValuesEnum.ownerRoleName,
        description: `Owner Of The Store`,
        permissions: roleIds,
        store: new ObjectId(globalValuesEnum.placeholderObjectId),
        version: 1,
        status: statusTypeEnum.active,
        created_at: new Date(),
        created_by: new ObjectId(req.userId),
      }, { session });

      const generalCustomerResponse = await customers.insertOne({
        customer_id: globalValuesEnum.firstCustomerId,
        name: globalValuesEnum.generalCustomerName,
        description: "-",
        phone: "-",
        email: "-",
        address: "-",
        store: new ObjectId(globalValuesEnum.placeholderObjectId),
        version: 1,
        status: statusTypeEnum.active,
        created_at: new Date(),
        created_by: new ObjectId(req.userId),
      }, { session });

      const storeInsertData = {
        name: validatedData.name,
        appearance: {
          title: validatedData.name.replace(/[^a-zA-Z0-9 ]/g, "").substring(0, 15).trim(),
          favicon: "",
          logo: "",
          color_scheme: getRandomColor(),
          theme: "default",
          font_family: "Poppins"
        },
        domain: {
          sub_domain: createSlug(validatedData.name) + "-" + generateRandomString(5),
          custom_domain: "",
          under_maintainance: false,
        },
        contact: {
          phone: "",
          email: "",
          address: "",
        },
        payments: [],
        plugins: [],
        settings: {
          category: validatedData.category,
          currency: "NPR",
          language: "en-US",
          timezone: "Asia/Kathmandu",
          date_format: "AD",
          general_customer_id: new ObjectId(generalCustomerResponse.insertedId),
          owner_role_id: new ObjectId(ownerRoleResponse.insertedId),
        },
        delivery: [],
        social: [],
        subscription: {
          plan: planTypeEnum.free,
          updated_at: new Date(),
          expires_at: new Date(),
        },
        users: [{
          user_id: new ObjectId(req.userId),
          role_id: new ObjectId(ownerRoleResponse.insertedId),
          created_at: new Date(),
          created_by: new ObjectId(req.userId),
          status: statusTypeEnum.active,
        }],
        verification: [],
        version: 1,
        status: statusTypeEnum.active,
        created_at: new Date(),
        created_by: new ObjectId(req.userId),
      };
      const createStoreResponse = await stores.insertOne(storeInsertData, { session });

      const updateStoreIdInRole = await roles.updateOne(
        {
          _id: new ObjectId(ownerRoleResponse.insertedId),
        },
        {
          $set: {
            store: new ObjectId(createStoreResponse.insertedId),
          }
        },
        { session }
      );

      if (updateStoreIdInRole.modifiedCount === 0) {
        throw new FatalError(500, "Failed To Update Store Id In Role");
      }

      const updateStoreIdInCustomer = await customers.updateOne(
        {
          _id: new ObjectId(generalCustomerResponse.insertedId),
        },
        {
          $set: {
            store: new ObjectId(createStoreResponse.insertedId),
          }
        },
        { session }
      );

      if (updateStoreIdInCustomer.modifiedCount === 0) {
        throw new FatalError(500, "Failed To Update Store Id In Customer");
      }

      return res.status(201).json({
        _id: createStoreResponse.insertedId,
        name: storeInsertData.name,
        appearance: {
          color_scheme: storeInsertData.appearance.color_scheme,
          logo: storeInsertData.appearance.logo,
        },
        role: {
          _id: ownerRoleResponse.insertedId,
          name: globalValuesEnum.ownerRoleName,
        },
      });
    });
  }
  catch (error: any) {
    next(error);
  }
}

export const getStoresAction = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { db } = await ConnectToDatabase();
    const stores = db.collection("Stores");

    const storesList = await stores.aggregate([
      {
        $match: {
          "users.user_id": new ObjectId(req.userId),
          "users.status": statusTypeEnum.active
        }
      },
      {
        $lookup: {
          from: "Roles",
          localField: "users.role_id",
          foreignField: "_id",
          as: "role"
        }
      },
      {
        $project: {
          _id: 1,
          name: 1,
          appearance: {
            color_scheme: 1,
            logo: 1,
          },
          role: {
            $let: {
              vars: {
                matchedRole: {
                  $arrayElemAt: [
                    {
                      $filter: {
                        input: "$role",
                        as: "role",
                        cond: {
                          $eq: ["$$role._id", { $arrayElemAt: ["$users.role_id", 0] }]
                        }
                      }
                    },
                    0
                  ]
                }
              },
              in: {
                _id: "$$matchedRole._id",
                name: "$$matchedRole.name"
              }
            }
          },
        }
      }
    ]).toArray();

    res.status(200).json(storesList);
  }
  catch (error: any) {
    next(error);
  }
}

export const getStoreAction = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { db } = await ConnectToDatabase();
    const stores = db.collection("Stores");

    const initialResponse = await stores.aggregate([
      {
        $match: {
          _id: new ObjectId(req.storeId)
        }
      },
      {
        $lookup: {
          from: "Roles",
          localField: "users.role_id",
          foreignField: "_id",
          as: "role"
        }
      },
      {
        $project: {
          _id: 1,
          name: 1,
          appearance: 1,
          domain: 1,
          contact: 1,
          settings: 1,
          social: 1,
          subscription: 1,
          users: {
            $map: {
              input: "$users",
              as: "user",
              in: {
                user_id: "$$user.user_id",
                role_id: "$$user.role_id",
                created_at: "$$user.created_at",
                created_by: "$$user.created_by",
                status: "$$user.status",
                role: {
                  $let: {
                    vars: {
                      matchedRole: {
                        $arrayElemAt: [
                          {
                            $filter: {
                              input: "$role",
                              as: "role",
                              cond: {
                                $eq: ["$$role._id", "$$user.role_id"]
                              }
                            }
                          },
                          0
                        ]
                      }
                    },
                    in: {
                      _id: "$$matchedRole._id",
                      name: "$$matchedRole.name"
                    }
                  }
                }
              }
            }
          }
        }
      }
    ]).toArray();

    res.status(200).json(initialResponse);
  }
  catch (error: any) {
    next(error);
  }
}

export const updateStoreAction = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Validate input data using Zod schema
    const validatedData = UpdateStoreApiSchema.parse(req.body);

    const { db } = await ConnectToDatabase();
    const stores = db.collection("Stores");

    // Prepare update object with validated data
    const updateData = generateUpdateObject(validatedData);

    // Add metadata for tracking
    updateData.updated_at = new Date();
    updateData.updated_by = new ObjectId(req.userId);

    // Perform the update
    const updateResponse = await stores.updateOne(
      {
        _id: new ObjectId(req.storeId)
      },
      {
        $set: updateData
      }
    );

    if (updateResponse.modifiedCount === 0) {
      throw new FatalError(400, "No Changes Were Made To The Store");
    }

    // Fetch and return updated store data
    const updatedStore = await stores.findOne(
      { _id: new ObjectId(req.storeId) },
      {
        projection: {
          _id: 1,
          name: 1,
          appearance: 1,
          domain: 1,
          contact: 1,
          settings: 1,
          social: 1,
          updated_at: 1,
          version: 1
        }
      }
    );

    res.status(200).json({
      message: "Store Updated Successfully",
      store: updatedStore
    });
  }
  catch (error: any) {
    next(error);
  }
}