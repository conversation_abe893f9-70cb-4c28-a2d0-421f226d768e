import type { NextFunction, Request, Response } from "express";

interface ActivityLog {
  url: string;
  ip: string;
  storeId: string | null;
  userId: string | null;
  requestBody: any;
  queryParams: any;
  responseStatus: number;
  method: string;
  timestamp: Date;
}

export default function loggerMiddleware(req: Request, res: Response, next: NextFunction) {
  const startTime = new Date();

  // Store original res.json and res.status methods
  const originalJson = res.json;
  const originalSend = res.send;
  let statusCode = 200;
  let responseLogged = false;

  // Override res.status to capture status code
  const originalStatus = res.status;
  res.status = function(code: number) {
    statusCode = code;
    return originalStatus.call(this, code);
  };

  // Override res.json to capture response and log activity
  res.json = function(body: any) {
    if (!responseLogged) {
      logActivity(req, statusCode, startTime);
      responseLogged = true;
    }
    return originalJson.call(this, body);
  };

  // Handle cases where response is sent without json (like res.send)
  res.send = function(body: any) {
    if (!responseLogged) {
      logActivity(req, statusCode, startTime);
      responseLogged = true;
    }
    return originalSend.call(this, body);
  };

  next();
}

function logActivity(req: Request, responseStatus: number, timestamp: Date) {
  try {
    // Skip logging if database is not available
    if (!req.db) {
      return;
    }

    // Get client IP address
    const ip = req.ip ||
               req.socket.remoteAddress ||
               req.headers['x-forwarded-for'] as string ||
               req.headers['x-real-ip'] as string ||
               'unknown';

    const activityLog: ActivityLog = {
      url: req.path,
      ip: Array.isArray(ip) ? ip[0] : ip,
      storeId: req.storeId || null,
      userId: req.userId || null,
      requestBody: req.body || {},
      queryParams: req.query || {},
      responseStatus,
      method: req.method,
      timestamp
    };

    // Insert activity log into database (fire and forget)
    const activityLogs = req.db.collection("ActivityLogs");
    activityLogs.insertOne(activityLog).catch((error: any) => {
      console.error("Failed to log activity:", error);
    });
  } catch (error: any) {
    console.error("Error in activity logging:", error);
  }
}