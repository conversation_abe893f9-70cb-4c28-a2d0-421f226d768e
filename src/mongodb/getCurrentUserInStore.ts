import { ObjectId } from "mongodb";

const getCurrentUserInStore = (storeId: string | undefined) => ({
  $lookup: {
    from: "Stores",
    let: {
      userId: "$_id",
      storeId: new ObjectId(storeId)
    },
    pipeline: [
      {
        $match: {
          $expr: {
            $and: [
              { $ne: ["$$storeId", null] },
              { $eq: ["$_id", "$$storeId"] },
              { $in: ["$$userId", "$users.user_id"] },
              {
                $gt: [
                  {
                    $size: {
                      $filter: {
                        input: "$users",
                        as: "user",
                        cond: {
                          $and: [
                            { $eq: ["$$user.user_id", "$$userId"] },
                            { $eq: ["$$user.status", "active"] }
                          ]
                        }
                      }
                    }
                  },
                  0
                ]
              }
            ]
          }
        }
      }
    ],
    as: "currentUserInStore"
  }
});

export default getCurrentUserInStore;