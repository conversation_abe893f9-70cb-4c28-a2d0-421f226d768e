import { colorNames } from "../config/color-names";
import { generateRandomString } from "./cryptography";

export function createSlug(string: string) {
  const slug = string.toLowerCase().replace(/[^a-zA-Z0-9 -]/g, "").replace(/\s+/g, "-");

  if (!slug) {
    return generateRandomString(10);
  }
  return slug;
}

export function convertToPascalCase(string: string) {
  if (!string) return string;
  
  const words = string
    .split(/[-_]|(?=[A-Z])/)
    .filter(word => word)
    .map(word => word.toLowerCase());
  
  return words
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');
}

export function getRandomColor() {
  return Object.keys(colorNames)[Math.floor(Math.random() * Object.keys(colorNames).length)] as keyof typeof colorNames;
}
