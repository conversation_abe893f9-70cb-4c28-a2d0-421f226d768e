import crypto from "crypto";

export function hashPassword(password: string) {
  const salt = process.env.HASH_SECRET_SALT;
  const pepper = process.env.HASH_SECRET_PEPPER;

  if (!salt || !pepper) {
    throw new Error("SECRET_SALT or SECRET_PEPPER is not defined in the environment variables");
  }

  const rounds = 100;
  let hashedPassword = password;

  hashedPassword += salt + pepper;

  for (let i = 0; i < rounds; i++) {
    hashedPassword = crypto.pbkdf2Sync(hashedPassword, salt + pepper, rounds, 128, "sha512").toString("hex");
  }

  return hashedPassword;
}

export function generateRandomString(length: number) {
  const randomString = crypto.randomBytes(Math.ceil(length / 2)).toString("hex").slice(0, length);
  return randomString;
}