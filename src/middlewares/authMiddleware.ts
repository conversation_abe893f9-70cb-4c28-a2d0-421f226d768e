import jwt from "jsonwebtoken";
import { ObjectId } from "mongodb";
import getCurrentUser from "../mongodb/getCurrentUser";
import getCurrentUserInStore from "../mongodb/getCurrentUserInStore";
import getUserPermissionsInStore from "../mongodb/getUserPermissionsInStore";
import { FatalError } from "../functions/errors";
import { getPermissionByActionName } from "../config/roles.config";
import { getAuthTokenAndStoreId, getControllerAndAction } from "../functions/middleware";
import { Actions, Controllers } from "../enums/baseEnum";
import type { JwtToken } from "../typings/JwtToken";
import type { NextFunction, Request, Response } from "express";

export default async function authMiddleware(req: Request, _res: Response, next: NextFunction) {
  try {
    const { controller, action } = getControllerAndAction(req);

    if (controller === Controllers.Auth && (action === Actions.Auth.Login || action === Actions.Auth.SignUp)) {
      return next();
    }

    const { authToken, storeId, isStoreIdOptional } = getAuthTokenAndStoreId(req);
    if (!authToken) {
      throw new FatalError(401, "No Auth Token Provided");
    }

    if (!isStoreIdOptional && !storeId) {
      throw new FatalError(400, "Store ID Is Required");
    }

    if (!isStoreIdOptional && !ObjectId.isValid(storeId)) {
      throw new FatalError(400, "Invalid Store ID");
    }

    let decodedToken = {} as JwtToken;
    try {
      decodedToken = jwt.verify(authToken, process.env.JWT_SECRET as string) as JwtToken;
    }
    catch (error) {
      throw new FatalError(401, "Auth Token Is Tampered or Expired");
    }
    
    const requiredPermission = getPermissionByActionName(action);
    const users = req.db.collection("Users");
    const initialResponse = await users.aggregate([
      getCurrentUser(decodedToken._id, decodedToken.session_id),
      ...(!isStoreIdOptional ? [getCurrentUserInStore(storeId), getUserPermissionsInStore()] : []),
      {
        $project: {
          userPermissionsInStore: 1,
          currentUserInStore: {
            _id: 1
          },
        }
      }
    ]).toArray();

    if (initialResponse.length === 0) {
      throw new FatalError(401, "Your Session Has Expired");
    }

    if (!isStoreIdOptional && initialResponse[0].currentUserInStore.length === 0) {
      throw new FatalError(403, "You Don't Have Access To This Store");
    }

    if (requiredPermission) {
      const availablePermissions = initialResponse[0].userPermissionsInStore[0].permissions;
      if (!availablePermissions.includes(requiredPermission.id)) {
          throw new FatalError(403, `You Are Not Allowed To ${requiredPermission.label}`);
        }
    }

    req.userId = decodedToken._id;
    req.sessionId = decodedToken.session_id;
    req.storeId = storeId;

    next();
  }
  catch (error) {
    next(error);
  }
}