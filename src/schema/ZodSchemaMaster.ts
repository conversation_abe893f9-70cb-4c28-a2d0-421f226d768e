import { z } from "zod";
import * as cheerio from "cheerio";
import { isEmpty, isNull } from "lodash";

function isObjectId(value: string) {
  return /^[0-9a-fA-F]{24}$/.test(value);
}

export const zValidateNumber = ({ fieldName, min, max, isInt, isNonNegative }: { fieldName: string, min?: number, max?: number, isInt?: boolean, isNonNegative?: boolean }) => {
  let schema = z.number({
    invalid_type_error: `${fieldName} Must Be A Number`,
    required_error: `${fieldName} Is Required`
  });

  if (isNonNegative !== undefined) {
    schema = schema.nonnegative(`${fieldName} Cannot Be Negative`);
  }

  if (isInt !== undefined) {
    schema = schema.int(`${fieldName} Cannot Be A Decimal`);
  }

  if (min !== undefined) {
    schema = schema.min(min, `${fieldName} Cannot Be Less Than ${min}`);
  }

  if (max !== undefined) {
    schema = schema.max(max, `${fieldName} Cannot Be More Than ${max}`);
  }

  return schema;
}

export const zValidateString = ({ fieldName, minLength, maxLength, length, regex }: { fieldName: string, minLength?: number, maxLength?: number, length?: number, regex?: RegExp }) => {
  let schema = z.string({
    invalid_type_error: `${fieldName} Must Be A String`,
    required_error: `${fieldName} Is Required`
  });

  if (minLength !== undefined) {
    schema = schema.min(minLength, `${fieldName} Cannot Be Less Than ${minLength} Characters`);
  }

  if (maxLength !== undefined) {
    schema = schema.max(maxLength, `${fieldName} Cannot Be More Than ${maxLength} Characters`);
  }

  if (length !== undefined) {
    schema = schema.length(length, `${fieldName} Must Be ${length} Characters`);
  }

  if (regex !== undefined) {
    schema = schema.regex(regex, `${fieldName} Contains Invalid Characters`);
  }

  schema = schema.trim();

  return schema;
}

export const zValidateEmail = ({ fieldName, optional }: { fieldName: string, optional?: boolean }) => {
  if (optional) {
    return z.union([
      z.literal(""),
      z.string({
        required_error: `${fieldName} Is Required`,
        invalid_type_error: `${fieldName} Must Be A String`
      })
      .email(`Please Enter A Valid ${fieldName}`)
      .trim()
    ]);
  }

  return z.string({
    required_error: `${fieldName} Is Required`,
    invalid_type_error: `${fieldName} Must Be A String`
  })
  .email(`Please Enter A Valid ${fieldName}`)
  .trim();
}

export const zValidatePassword = ({ fieldName }: { fieldName: string }) => {
  return z.string({
    required_error: `${fieldName} Is Required`,
    invalid_type_error: `${fieldName} Must Be A String`
  })
  .min(6, `${fieldName} Cannot Be Less Than 6 Characters`)
  .max(512, `${fieldName} Cannot Be More Than 512 Characters`);
}

export const zValidateRichText = ({ fieldName, minLength, maxLength }: { fieldName: string, minLength?: number, maxLength?: number }) => {
  let schema = z.string({
    invalid_type_error: `${fieldName} Must Be A String`,
    required_error: `${fieldName} Is Required`
  });

  if (minLength !== undefined) {
    schema = schema.refine(
      (value) => {
        const richText = cheerio.load(value).text();
        return richText.length >= minLength;
      },
      {
        message: `${fieldName} Must Be At Least ${minLength} Characters`,
      }
    ) as unknown as z.ZodString;
  }

  if (maxLength !== undefined) {
    schema = schema.refine(
      (value) => {
        const richText = cheerio.load(value).text();
        return richText.length <= maxLength;
      },
      {
        message: `${fieldName} Cannot Be More Than ${maxLength} Characters`,
      }
    ) as unknown as z.ZodString;
  }
}

export const zValidateObjectId = ({ fieldName }: { fieldName: string }) => {
  return z.string({
    invalid_type_error: `${fieldName} Must Be A String`,
    required_error: `${fieldName} Is Required`
  })
  .refine((value) => isObjectId(value), `${fieldName} Is Invalid`);
}

export const zValidateOptionalObjectId = ({ fieldName }: { fieldName: string }) => {
  return z.string({
    invalid_type_error: `${fieldName} Must Be A String`,
    required_error: `${fieldName} Is Required`
  })
  .nullable()
  .refine((value) => isNull(value) || isEmpty(value) || isObjectId(value), `${fieldName} Is Invalid`);
}

export const zValidateBoolean = ({ fieldName }: { fieldName: string }) => {
  return z.boolean({
    invalid_type_error: `${fieldName} Must Be A Boolean`,
    required_error: `${fieldName} Is Required`
  });
}

export const zValidateImageUrl = ({ fieldName }: { fieldName: string }) => {
  return z.string({
    invalid_type_error: `${fieldName} Must Be A String`,
    required_error: `${fieldName} Is Required`
  })
  .trim();
}

export const zValidateItemExistInArray = ({ fieldName, possibleValues }: { fieldName: string, possibleValues: string[] }) => {
  return z.string({
    invalid_type_error: `${fieldName} Must Be A String`,
    required_error: `${fieldName} Is Required`
  })
  .refine((value) => possibleValues.includes(value), `${fieldName} Is Invalid`);
}
