// import { parseError } from "../functions/errors";
// import type { CustomError } from "../typings/CustomError";
// import type { Request, Response, NextFunction } from "express";

// export default function errorMiddleware(error: CustomError, _req: Request, res: Response, _next: NextFunction) {
//   res.status(error.statusCode || 500).json(parseError(error));
// }


import { parseError } from "../functions/errors";
import type { CustomError } from "../typings/CustomError";
import type { Request, Response, NextFunction } from "express";

export default function errorMiddleware(
  error: CustomError,
  req: Request,
  res: Response,
  _next: NextFunction
) {
  const statusCode = error.statusCode || 500;

  const errorLog = {
    error: {
      name: error.name,
      message: error.message,
      statusCode,
      field: error.field || null,
      stack: process.env.NODE_ENV === "production" ? undefined : error.stack
    },
    request: {
      method: req.method,
      url: decodeURI(req.originalUrl),
      basePath: req.baseUrl || req.path,
      query: req.query,
      body: req.body,
      userId: (req as any).userId || null,
      ip: req.ip || req.socket.remoteAddress,
    },
    time: new Date().toISOString()
  };

  console.error("❌ Error caught in errorMiddleware:\n", JSON.stringify(errorLog, null, 2));

  res.status(statusCode).json(parseError(error));
}
