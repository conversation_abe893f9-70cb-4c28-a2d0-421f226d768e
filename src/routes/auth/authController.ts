import jwt from "jsonwebtoken";
import { ObjectId } from "mongodb";
import { FatalError } from "../../functions/errors";
import { loginMethodTypeEnum } from "../../enums/loginMethodTypeEnum";
import { generateRandomString, hashPassword } from "../../functions/cryptography";
import { DeleteSessionApiSchema, LoginApiSchema, SignUpApiSchema } from "../../schema/UserApiSchema";
import { statusTypeEnum } from "../../enums/statusTypeEnum";
import type { NextFunction, Request, Response } from "express";

export const loginAction = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const validatedData = LoginApiSchema.parse(req.body);

    const hashedPassword = hashPassword(validatedData.password);
    const sessionId = generateRandomString(32);
    const userAgent = req.headers["user-agent"];
    const loginMethod = validatedData.email ? loginMethodTypeEnum.email : loginMethodTypeEnum.phone;
    const users = req.db.collection("Users");

    const user = await users.findOneAndUpdate(
      {
        $or: [
          {
            email: {
              $eq: validatedData.email,
              $ne: null
            },
            password: hashedPassword,
          },
          {
            phone: {
              $eq: validatedData.phone,
              $ne: null
            },
            password: hashedPassword,
          }
        ]
      },
      {
        $push: {
          sessions: {
            session_id: sessionId,
            login_date: new Date(),
            last_online: new Date(),
            login_method: loginMethod,
            user_agent: userAgent,
          }
        } as any
      },
      {
        projection: {
          _id: 1,
        }
      }
    );

    if (!user) {
      throw new FatalError(403, "Invalid Email/ Phone or Password");
    }

    const jwtTokenData = {
      _id: user._id,
      session_id: sessionId,
    };

    const jwtToken = jwt.sign(jwtTokenData, process.env.JWT_SECRET as string, {
      expiresIn: "14d",
      algorithm: "HS512",
    });

    res.status(200).json({
      message: "Logged In Successfully",
      token: jwtToken,
    });
  }
  catch (error: any) {
    next(error);
  }
}

export const signUpAction = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const validatedData = SignUpApiSchema.parse(req.body);
    const users = req.db.collection("Users");

    const user = await users.findOne({
      $or: [
        {
          email: {
            $eq: validatedData.email,
            $ne: null
          },
        },
        {
          phone: {
            $eq: validatedData.phone,
            $ne: null
          },
        },
      ],
    });

    if (user) {
      if (user.email === validatedData.email) {
        throw new FatalError(409, "Email Already Used By Another Account", "email");
      }
      if (user.phone === validatedData.phone) {
        throw new FatalError(409, "Phone Already Used By Another Account", "phone");
      }
    }

    const newUser = {
      name: validatedData.name,
      email: validatedData.email,
      email_verified: false,
      phone: validatedData.phone,
      phone_verified: false,
      password: hashPassword(validatedData.password),
      sessions: [],
      version: 1,
      status: statusTypeEnum.active,
      created_at: new Date()
    };

    const createAccountResponse = await users.insertOne(newUser);

    if (!createAccountResponse.insertedId) {
      throw new FatalError(500, "Failed To Create Account");
    }

    res.status(201).json({
      message: "Account Created Successfully",
    });
  }
  catch (error: any) {
    next(error);
  }
}

export const deleteSessionAction = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const validatedData = DeleteSessionApiSchema.parse(req.body);
    const users = req.db.collection("Users");

    const deleteSession = await users.updateOne(
      {
        _id: new ObjectId(req.userId),
        "sessions.session_id": req.sessionId,
      },
      {
        $pull: {
          sessions: {
            session_id: {
              $regex: `^${validatedData.sessionId.replace(/\*+/g, ".*")}$`
            },
          },
        } as any
      }
    );

    if (deleteSession.modifiedCount === 0) {
      throw new FatalError(404, "Session Not Found");
    }

    res.status(200).json({
      message: "Session Deleted Successfully",
    });
  }
  catch (error: any) {
    next(error);
  }
}

export const deleteOtherSessionsAction = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const users = req.db.collection("Users");

    await users.updateOne(
      {
        _id: new ObjectId(req.userId),
        "sessions.session_id": req.sessionId
      },
      {
        $pull: {
          sessions: {
            session_id: {
              $ne: req.sessionId
            },
          },
        } as any
      }
    );

    res.status(200).json({
      message: "Successfully Logged Out From Other Sessions",
    });
  }
  catch (error: any) {
    next(error);
  }
}