import express from "express";
import { deleteOtherSessionsAction, deleteSessionAction, loginAction, signUpAction } from "./authController";

const authRoutes = express.Router();

authRoutes.post("/sign-up", signUpAction);
authRoutes.post("/login", loginAction);
authRoutes.post("/delete-session", deleteSessionAction);
authRoutes.post("/delete-other-sessions", deleteOtherSessionsAction);

export default authRoutes;