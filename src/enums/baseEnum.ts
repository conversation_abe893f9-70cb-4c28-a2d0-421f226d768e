export const Controllers = {
  Auth: "auth",
  User: "user",
  Store: "store",
  getAsArray() {
    const controllers = Object.values(this);
    const filteredControllers = [];
    for (const controller of controllers) {
      if (typeof controller === "string") {
        filteredControllers.push(controller);
      } 
    }
    return filteredControllers as Controller[];
  } 
} as const;

export const Actions = {
  Auth: {
    Login: "login",
    SignUp: "sign-up",
    DeleteSession: "delete-session",
    DeleteOtherSessions: "delete-other-sessions",
  },
  Store: {
    GetStore: "get-store",
    GetStores: "get-stores",
    CreateStore: "create-store",
    UpdateStore: "update-store",
    DeleteStore: "delete-store",
  },
  User: {
    GetProfile: "get-profile",
    UpdatePassword: "update-password",
  },
  getAsArray() {
    const actions = Object.values(this).reduce((acc, curr) => {
      if (typeof curr === "object") {
        return [...acc, ...Object.values(curr)];
      }
      return acc;
    }
    , [] as string[]) as Action[];
    return actions;
  }
} as const;

export type Controller = (typeof Controllers)[Exclude<keyof typeof Controllers, "getAsArray">];
export type Action = {[K in keyof typeof Actions]: (typeof Actions)[K] extends object ? (typeof Actions)[K][keyof (typeof Actions)[K]] : never}[keyof typeof Actions];