{"name": "ecommerce-backend", "version": "1.0.0", "description": "Ecommerce Backend App", "keywords": ["backend", "api"], "license": "ISC", "author": "Bibek Oli", "main": "index.js", "scripts": {"esbuild": "rimraf dist && esbuild src/index.ts --bundle --minify --platform=node --outfile=dist/bundle.js", "esstart": "node --env-file=.env dist/bundle.js", "build": "rimraf dist && tsc --build", "start": "ts-node ./src/index.ts", "dev": "nodemon ./src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "vercel-build": "echo hello"}, "dependencies": {"cheerio": "^1.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mongodb": "^6.13.1", "zod": "^3.24.2"}, "devDependencies": {"@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.15", "esbuild": "^0.25.0", "jest": "^29.7.0", "nodemon": "^3.1.9", "rimraf": "^6.0.1", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}